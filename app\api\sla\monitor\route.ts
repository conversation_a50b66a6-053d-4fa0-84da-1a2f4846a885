import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { slaMonitor } from '@/lib/sla-monitor';

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only admins can access monitor status
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get monitor status
    const status = slaMonitor.getStatus();

    return NextResponse.json({
      success: true,
      status
    });
  } catch (error) {
    console.error('Error in GET /api/sla/monitor:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only admins can control the monitor
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { action } = body;

    if (!action || !['start', 'stop', 'restart', 'run_cycle'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be start, stop, restart, or run_cycle' },
        { status: 400 }
      );
    }

    let message = '';
    
    switch (action) {
      case 'start':
        slaMonitor.start();
        message = 'SLA Monitor started successfully';
        break;
        
      case 'stop':
        slaMonitor.stop();
        message = 'SLA Monitor stopped successfully';
        break;
        
      case 'restart':
        slaMonitor.stop();
        setTimeout(() => slaMonitor.start(), 1000);
        message = 'SLA Monitor restarted successfully';
        break;
        
      case 'run_cycle':
        await slaMonitor.runMonitoringCycle();
        message = 'SLA monitoring cycle executed successfully';
        break;
    }

    const status = slaMonitor.getStatus();

    return NextResponse.json({
      success: true,
      message,
      status
    });
  } catch (error) {
    console.error('Error in POST /api/sla/monitor:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
