import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { slaMonitor } from "@/lib/sla-monitor";

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");
    const status = searchParams.get("status");
    const taskId = searchParams.get("taskId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    // Build where clause
    const where: {
      task: { organizationId: string };
      status?: string;
      taskId?: string;
    } = {
      task: {
        organizationId,
      },
    };

    if (status) {
      where.status = status;
    }

    if (taskId) {
      where.taskId = taskId;
    }

    // Get escalations with pagination
    const [escalations, total] = await Promise.all([
      prisma.escalation.findMany({
        where,
        include: {
          task: {
            select: {
              id: true,
              title: true,
              priority: true,
              status: true,
              assignedTo: true,
              assignedUser: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.escalation.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      escalations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error in GET /api/escalations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { taskId, reason } = body;

    if (!taskId || !reason) {
      return NextResponse.json(
        { error: "Task ID and reason are required" },
        { status: 400 }
      );
    }

    // Verify task exists and user has access
    const task = await prisma.task.findFirst({
      where: {
        id: taskId,
        organization: {
          users: {
            some: {
              id: session.user.id,
            },
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: "Task not found or access denied" },
        { status: 404 }
      );
    }

    // Trigger manual escalation
    await slaMonitor.triggerManualEscalation(taskId, reason, session.user.id);

    return NextResponse.json(
      {
        success: true,
        message: "Manual escalation created successfully",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error in POST /api/escalations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
