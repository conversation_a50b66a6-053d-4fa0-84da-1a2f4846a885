# Task 5: SLA & Escalation System

## 🎯 Objective

Implement a comprehensive SLA (Service Level Agreement) tracking and escalation system that monitors task deadlines, triggers escalations, and ensures timely task completion.

## 📋 Status

- **Priority**: P2 (Important)
- **Status**: ✅ Complete
- **Estimated Time**: 6-8 hours
- **Dependencies**: Task 4 (Routing Rules Engine)
- **Completed**: 2025-06-24

## 🔧 Technical Requirements

### Database Schema Updates

```prisma
model SLAPolicy {
  id                    String   @id @default(cuid())
  organizationId        String   @map("organization_id")
  name                  String
  description           String?
  taskType              String?  @map("task_type") // null = applies to all types
  priority              String?  // null = applies to all priorities
  responseTimeMinutes   Int      @map("response_time_minutes")
  resolutionTimeMinutes Int      @map("resolution_time_minutes")
  escalationLevels      Json     @default("[]") // Array of escalation configurations
  isActive              Boolean  @default(true) @map("is_active")
  isDefault             <PERSON>olean  @default(false) @map("is_default")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, name])
  @@map("sla_policies")
}

model TaskSLA {
  id                    String    @id @default(cuid())
  taskId                String    @unique @map("task_id")
  policyId              String    @map("policy_id")
  responseDeadline      DateTime  @map("response_deadline")
  resolutionDeadline    DateTime  @map("resolution_deadline")
  firstResponseAt       DateTime? @map("first_response_at")
  resolvedAt            DateTime? @map("resolved_at")
  responseBreached      Boolean   @default(false) @map("response_breached")
  resolutionBreached    Boolean   @default(false) @map("resolution_breached")
  currentEscalationLevel Int      @default(0) @map("current_escalation_level")
  escalationHistory     Json      @default("[]") // Array of escalation events
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  task   Task      @relation(fields: [taskId], references: [id], onDelete: Cascade)
  policy SLAPolicy @relation(fields: [policyId], references: [id])

  @@map("task_sla")
}

model EscalationEvent {
  id              String   @id @default(cuid())
  taskId          String   @map("task_id")
  level           Int
  triggeredAt     DateTime @default(now()) @map("triggered_at")
  triggeredBy     String   @map("triggered_by") // 'system' or user ID
  escalationType  String   @map("escalation_type") // 'response_breach', 'resolution_breach', 'manual'
  escalatedTo     String?  @map("escalated_to") // User ID or team ID
  previousAgent   String?  @map("previous_agent")
  reason          String?
  resolved        Boolean  @default(false)
  resolvedAt      DateTime? @map("resolved_at")

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("escalation_events")
}
```

### TypeScript Types

```typescript
export interface SLAPolicy {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  taskType?: string;
  priority?: string;
  responseTimeMinutes: number;
  resolutionTimeMinutes: number;
  escalationLevels: EscalationLevel[];
  isActive: boolean;
  isDefault: boolean;
}

export interface EscalationLevel {
  level: number;
  triggerAfterMinutes: number;
  escalateTo: string; // 'manager', 'team_lead', 'specific_agent', 'external'
  escalateToId?: string; // Specific user ID if escalateTo is 'specific_agent'
  notificationChannels: string[]; // 'email', 'slack', 'sms'
  actions: string[]; // 'reassign', 'increase_priority', 'notify_manager'
}

export interface TaskSLAStatus {
  id: string;
  taskId: string;
  policyId: string;
  responseDeadline: Date;
  resolutionDeadline: Date;
  firstResponseAt?: Date;
  resolvedAt?: Date;
  responseBreached: boolean;
  resolutionBreached: boolean;
  currentEscalationLevel: number;
  escalationHistory: EscalationEvent[];
}

export interface EscalationEvent {
  id: string;
  taskId: string;
  level: number;
  triggeredAt: Date;
  triggeredBy: string;
  escalationType: string;
  escalatedTo?: string;
  previousAgent?: string;
  reason?: string;
  resolved: boolean;
  resolvedAt?: Date;
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Updates (30 minutes)

1. **Update Prisma Schema**

   - Add SLA policy, task SLA, and escalation event models
   - Update Task model to include SLA relation

2. **Create Migration**

   ```bash
   npx prisma migrate dev --name add-sla-escalation-system
   ```

3. **Update Seed Data**
   - Add default SLA policies for demo organization
   - Create sample escalation configurations

### Step 2: SLA Management System (120 minutes)

1. **Create `lib/sla-manager.ts`**

   ```typescript
   export class SLAManager {
     // SLA Policy Management
     async createSLAPolicy(
       organizationId: string,
       policy: Partial<SLAPolicy>
     ): Promise<SLAPolicy> {
       // Create new SLA policy
     }

     async getSLAPolicies(organizationId: string): Promise<SLAPolicy[]> {
       // Get all SLA policies for organization
     }

     async getApplicablePolicy(task: Task): Promise<SLAPolicy | null> {
       // Find the most specific SLA policy for a task
     }

     // Task SLA Tracking
     async createTaskSLA(task: Task): Promise<TaskSLAStatus> {
       const policy = await this.getApplicablePolicy(task);
       if (!policy) return null;

       const now = new Date();
       const responseDeadline = new Date(
         now.getTime() + policy.responseTimeMinutes * 60000
       );
       const resolutionDeadline = new Date(
         now.getTime() + policy.resolutionTimeMinutes * 60000
       );

       return prisma.taskSLA.create({
         data: {
           taskId: task.id,
           policyId: policy.id,
           responseDeadline,
           resolutionDeadline,
         },
       });
     }

     async updateTaskSLA(
       taskId: string,
       updates: Partial<TaskSLAStatus>
     ): Promise<TaskSLAStatus> {
       // Update task SLA status
     }

     async markFirstResponse(taskId: string): Promise<void> {
       // Mark when first response was provided
       await prisma.taskSLA.update({
         where: { taskId },
         data: { firstResponseAt: new Date() },
       });
     }

     async markResolved(taskId: string): Promise<void> {
       // Mark task as resolved
       await prisma.taskSLA.update({
         where: { taskId },
         data: { resolvedAt: new Date() },
       });
     }

     // SLA Monitoring
     async checkSLABreaches(): Promise<TaskSLAStatus[]> {
       const now = new Date();

       // Find tasks with breached SLAs
       const breachedTasks = await prisma.taskSLA.findMany({
         where: {
           OR: [
             {
               responseDeadline: { lt: now },
               firstResponseAt: null,
               responseBreached: false,
             },
             {
               resolutionDeadline: { lt: now },
               resolvedAt: null,
               resolutionBreached: false,
             },
           ],
         },
         include: { task: true, policy: true },
       });

       // Update breach status
       for (const taskSLA of breachedTasks) {
         const updates: any = {};

         if (taskSLA.responseDeadline < now && !taskSLA.firstResponseAt) {
           updates.responseBreached = true;
         }

         if (taskSLA.resolutionDeadline < now && !taskSLA.resolvedAt) {
           updates.resolutionBreached = true;
         }

         await prisma.taskSLA.update({
           where: { id: taskSLA.id },
           data: updates,
         });
       }

       return breachedTasks;
     }

     async getSLAMetrics(
       organizationId: string,
       timeframe: "day" | "week" | "month"
     ): Promise<any> {
       // Calculate SLA compliance metrics
     }
   }
   ```

### Step 3: Escalation Engine (150 minutes)

1. **Create `lib/escalation-engine.ts`**

   ```typescript
   export class EscalationEngine {
     async processEscalations(): Promise<void> {
       const slaManager = new SLAManager();
       const breachedTasks = await slaManager.checkSLABreaches();

       for (const taskSLA of breachedTasks) {
         await this.handleTaskEscalation(taskSLA);
       }
     }

     async handleTaskEscalation(taskSLA: TaskSLAStatus): Promise<void> {
       const policy = await prisma.slaPolicy.findUnique({
         where: { id: taskSLA.policyId },
       });

       if (!policy || !policy.escalationLevels) return;

       const escalationLevels = policy.escalationLevels as EscalationLevel[];
       const nextLevel = taskSLA.currentEscalationLevel + 1;

       const escalationConfig = escalationLevels.find(
         (level) => level.level === nextLevel
       );
       if (!escalationConfig) return;

       // Check if enough time has passed for this escalation level
       const task = await prisma.task.findUnique({
         where: { id: taskSLA.taskId },
       });
       const timeSinceCreation = Date.now() - task.createdAt.getTime();
       const triggerTime = escalationConfig.triggerAfterMinutes * 60000;

       if (timeSinceCreation >= triggerTime) {
         await this.executeEscalation(taskSLA, escalationConfig);
       }
     }

     async executeEscalation(
       taskSLA: TaskSLAStatus,
       config: EscalationLevel
     ): Promise<void> {
       const task = await prisma.task.findUnique({
         where: { id: taskSLA.taskId },
         include: { assignedUser: true },
       });

       // Create escalation event
       const escalationEvent = await prisma.escalationEvent.create({
         data: {
           taskId: taskSLA.taskId,
           level: config.level,
           triggeredBy: "system",
           escalationType: taskSLA.responseBreached
             ? "response_breach"
             : "resolution_breach",
           previousAgent: task.assignedTo,
         },
       });

       // Execute escalation actions
       for (const action of config.actions) {
         await this.executeEscalationAction(action, task, config);
       }

       // Update task SLA escalation level
       await prisma.taskSLA.update({
         where: { id: taskSLA.id },
         data: {
           currentEscalationLevel: config.level,
           escalationHistory: {
             push: escalationEvent,
           },
         },
       });

       // Send notifications
       await this.sendEscalationNotifications(task, config, escalationEvent);
     }

     async executeEscalationAction(
       action: string,
       task: Task,
       config: EscalationLevel
     ): Promise<void> {
       switch (action) {
         case "reassign":
           await this.reassignTask(task, config);
           break;
         case "increase_priority":
           await this.increasePriority(task);
           break;
         case "notify_manager":
           await this.notifyManager(task);
           break;
         case "create_routing_rule":
           await this.createUrgentRoutingRule(task);
           break;
       }
     }

     async reassignTask(task: Task, config: EscalationLevel): Promise<void> {
       let newAssignee: string | null = null;

       switch (config.escalateTo) {
         case "manager":
           newAssignee = await this.findManager(task.assignedTo);
           break;
         case "team_lead":
           newAssignee = await this.findTeamLead(task.organizationId);
           break;
         case "specific_agent":
           newAssignee = config.escalateToId;
           break;
         case "best_available":
           newAssignee = await this.findBestAvailableAgent(task);
           break;
       }

       if (newAssignee) {
         await prisma.task.update({
           where: { id: task.id },
           data: {
             assignedTo: newAssignee,
             assignedAt: new Date(),
             status: "escalated",
           },
         });

         // Log task event
         await prisma.taskEvent.create({
           data: {
             taskId: task.id,
             type: "escalated",
             description: `Task escalated from ${task.assignedTo} to ${newAssignee}`,
             metadata: { escalationLevel: config.level },
           },
         });
       }
     }

     async increasePriority(task: Task): Promise<void> {
       const priorityMap = {
         low: "medium",
         medium: "high",
         high: "urgent",
         urgent: "urgent", // Already at highest
       };

       const newPriority = priorityMap[task.priority] || "urgent";

       await prisma.task.update({
         where: { id: task.id },
         data: { priority: newPriority },
       });
     }
   }
   ```

### Step 4: SLA Monitoring Service (90 minutes)

1. **Create `lib/sla-monitor.ts`**

   ```typescript
   export class SLAMonitor {
     private intervalId: NodeJS.Timeout | null = null;

     start(intervalMinutes: number = 5): void {
       this.intervalId = setInterval(async () => {
         await this.runMonitoringCycle();
       }, intervalMinutes * 60000);
     }

     stop(): void {
       if (this.intervalId) {
         clearInterval(this.intervalId);
         this.intervalId = null;
       }
     }

     async runMonitoringCycle(): Promise<void> {
       try {
         const escalationEngine = new EscalationEngine();
         await escalationEngine.processEscalations();

         // Update SLA metrics
         await this.updateSLAMetrics();

         // Clean up old escalation events
         await this.cleanupOldEvents();
       } catch (error) {
         console.error("SLA monitoring cycle failed:", error);
       }
     }

     async updateSLAMetrics(): Promise<void> {
       // Calculate and cache SLA compliance metrics
       const organizations = await prisma.organization.findMany();

       for (const org of organizations) {
         const metrics = await this.calculateSLAMetrics(org.id);

         // Store metrics in cache or database
         await this.storeSLAMetrics(org.id, metrics);
       }
     }

     async calculateSLAMetrics(organizationId: string): Promise<any> {
       const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

       const slaData = await prisma.taskSLA.findMany({
         where: {
           createdAt: { gte: thirtyDaysAgo },
           task: { organizationId },
         },
         include: { task: true },
       });

       const totalTasks = slaData.length;
       const responseBreaches = slaData.filter(
         (s) => s.responseBreached
       ).length;
       const resolutionBreaches = slaData.filter(
         (s) => s.resolutionBreached
       ).length;

       return {
         totalTasks,
         responseCompliance:
           ((totalTasks - responseBreaches) / totalTasks) * 100,
         resolutionCompliance:
           ((totalTasks - resolutionBreaches) / totalTasks) * 100,
         avgResponseTime: this.calculateAvgResponseTime(slaData),
         avgResolutionTime: this.calculateAvgResolutionTime(slaData),
       };
     }
   }
   ```

### Step 5: API Endpoints (75 minutes)

1. **SLA Policies API** - `app/api/sla/policies/route.ts`

   - GET: List SLA policies
   - POST: Create SLA policy

2. **Task SLA Status API** - `app/api/tasks/[id]/sla/route.ts`

   - GET: Get task SLA status
   - PATCH: Update SLA status (mark response/resolution)

3. **Escalation Management API** - `app/api/escalations/route.ts`

   - GET: List escalation events
   - POST: Manual escalation

4. **SLA Metrics API** - `app/api/sla/metrics/route.ts`
   - GET: Get SLA compliance metrics

### Step 6: Integration with Task Lifecycle (45 minutes)

1. **Update Task Creation**

   ```typescript
   // In task creation API
   const task = await prisma.task.create({ data: taskData });

   // Create SLA tracking
   const slaManager = new SLAManager();
   await slaManager.createTaskSLA(task);
   ```

2. **Update Task Status Changes**

   ```typescript
   // When task status changes to 'in_progress'
   if (newStatus === "in_progress" && oldStatus === "assigned") {
     await slaManager.markFirstResponse(taskId);
   }

   // When task is completed
   if (newStatus === "completed") {
     await slaManager.markResolved(taskId);
   }
   ```

## 🧪 Testing Requirements

### Unit Tests

- SLA policy matching logic
- Escalation trigger conditions
- SLA metrics calculations
- Escalation action execution

### Integration Tests

- End-to-end SLA tracking
- Escalation workflow execution
- API endpoints functionality
- Integration with task lifecycle

### Manual Testing

- Create SLA policies with different configurations
- Test escalation triggers and actions
- Verify SLA breach detection
- Test manual escalation functionality

## 📝 Acceptance Criteria

- [x] Organizations can define SLA policies for different task types
- [x] Tasks automatically get SLA tracking based on applicable policies
- [x] SLA breaches are detected and trigger escalations
- [x] Escalation actions execute correctly (reassignment, priority increase, etc.)
- [x] SLA compliance metrics are calculated and available
- [x] Manual escalation is possible
- [x] Escalation history is maintained for audit purposes
- [x] Integration with routing rules for escalation actions

## 🔗 Dependencies for Next Tasks

This task enables:

- **Task 6**: Performance metrics include SLA compliance
- **Task 7**: UI components for SLA management and monitoring

## 📋 Deliverables

1. SLA policy and tracking database schema
2. SLA management system
3. Escalation engine with configurable actions
4. SLA monitoring service
5. API endpoints for SLA and escalation management
6. Integration with task lifecycle
7. SLA compliance metrics calculation
8. Comprehensive unit tests

**Next Task**: [Task 6: Performance Metrics](./task6-performance-metrics.md)
