import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getDemoOrgId } from "@/lib/db-utils";
import { randomUUID } from "crypto";
import type { EnhancedRoutingStrategy } from "@/lib/types";

// GET /api/routing/strategies - List available routing strategies
export async function GET() {
  try {
    const organizationId = await getDemoOrgId();

    const strategies = await prisma.routingStrategy.findMany({
      where: {
        organizationId: organizationId,
        isActive: true,
      },
      orderBy: [{ isDefault: "desc" }, { name: "asc" }],
    });

    // If no strategies exist, create default ones
    if (strategies.length === 0) {
      await createDefaultStrategies(organizationId);

      const newStrategies = await prisma.routingStrategy.findMany({
        where: {
          organizationId: organizationId,
          isActive: true,
        },
        orderBy: [{ isDefault: "desc" }, { name: "asc" }],
      });

      return NextResponse.json({
        success: true,
        data: newStrategies,
        message: "Default routing strategies created and retrieved",
      });
    }

    return NextResponse.json({
      success: true,
      data: strategies,
      message: "Routing strategies retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching routing strategies:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "FETCH_STRATEGIES_FAILED",
          message: "Failed to fetch routing strategies",
        },
      },
      { status: 500 }
    );
  }
}

// POST /api/routing/strategies - Create a new routing strategy
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, strategy, configuration, isDefault } = body;

    // Validate required fields
    if (!name || !strategy) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "MISSING_REQUIRED_FIELDS",
            message: "Name and strategy are required",
          },
        },
        { status: 400 }
      );
    }

    // Validate strategy type
    const validStrategies: EnhancedRoutingStrategy[] = [
      "weighted_round_robin",
      "best_skill_match",
      "performance_based",
      "hybrid_intelligent",
    ];

    if (!validStrategies.includes(strategy)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "INVALID_STRATEGY",
            message: `Invalid strategy. Must be one of: ${validStrategies.join(
              ", "
            )}`,
          },
        },
        { status: 400 }
      );
    }

    const organizationId = await getDemoOrgId();

    // Check if strategy name already exists
    const existingStrategy = await prisma.routingStrategy.findFirst({
      where: {
        organizationId: organizationId,
        name: name,
      },
    });

    if (existingStrategy) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "STRATEGY_NAME_EXISTS",
            message: "A strategy with this name already exists",
          },
        },
        { status: 409 }
      );
    }

    // If this is set as default, unset other defaults
    if (isDefault) {
      await prisma.routingStrategy.updateMany({
        where: {
          organizationId: organizationId,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Create the new strategy
    const newStrategy = await prisma.routingStrategy.create({
      data: {
        id: randomUUID(),
        organizationId: organizationId,
        name,
        strategy,
        isDefault: isDefault || false,
        isActive: true,
        configuration: configuration || {},
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: newStrategy,
        message: "Routing strategy created successfully",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating routing strategy:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "CREATE_STRATEGY_FAILED",
          message: "Failed to create routing strategy",
        },
      },
      { status: 500 }
    );
  }
}

// Helper function to create default strategies
async function createDefaultStrategies(organizationId: string) {
  const defaultStrategies = [
    {
      name: "Weighted Round Robin",
      strategy: "weighted_round_robin",
      isDefault: true,
      configuration: {
        description:
          "Balances workload, skills, and performance with equal weighting",
      },
    },
    {
      name: "Best Skill Match",
      strategy: "best_skill_match",
      isDefault: false,
      configuration: {
        description:
          "Prioritizes agents with the best skill match for the task",
      },
    },
    {
      name: "Performance Based",
      strategy: "performance_based",
      isDefault: false,
      configuration: {
        description: "Prioritizes agents with the best historical performance",
      },
    },
    {
      name: "Hybrid Intelligent",
      strategy: "hybrid_intelligent",
      isDefault: false,
      configuration: {
        description:
          "Dynamically adjusts weighting based on task characteristics",
      },
    },
  ];

  for (const strategyData of defaultStrategies) {
    await prisma.routingStrategy.create({
      data: {
        id: randomUUID(),
        organizationId: organizationId,
        ...strategyData,
      },
    });
  }
}
