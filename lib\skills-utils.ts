import { prisma } from './prisma';
import { AgentSkill, TaskRequiredSkill } from './types';

export class SkillsManager {
  // Agent skills management
  async addSkillToAgent(agentId: string, skillName: string, proficiencyLevel: number = 1) {
    // Validate skill exists in catalog
    const skill = await prisma.skillsCatalog.findFirst({
      where: {
        name: skillName,
        isActive: true
      }
    });
    
    if (!skill) {
      throw new Error('Skill not found in catalog');
    }

    // Add skill to agent
    return await prisma.agentSkill.create({
      data: {
        agentId,
        skillName,
        proficiencyLevel: Math.min(Math.max(proficiencyLevel, 1), 5) // Ensure 1-5 range
      }
    });
  }
  
  async removeSkillFromAgent(agentId: string, skillName: string) {
    return await prisma.agentSkill.delete({
      where: {
        agentId_skillName: {
          agentId,
          skillName
        }
      }
    });
  }
  
  async updateAgentSkillLevel(agentId: string, skillName: string, proficiencyLevel: number) {
    return await prisma.agentSkill.update({
      where: {
        agentId_skillName: {
          agentId,
          skillName
        }
      },
      data: {
        proficiencyLevel: Math.min(Math.max(proficiencyLevel, 1), 5)
      }
    });
  }
  
  async getAgentSkills(agentId: string) {
    return await prisma.agentSkill.findMany({
      where: { agentId }
    });
  }
  
  // Task skills management
  async addSkillRequirementToTask(taskId: string, skillName: string, requiredLevel: number = 1) {
    // Validate skill exists in catalog
    const skill = await prisma.skillsCatalog.findFirst({
      where: {
        name: skillName,
        isActive: true
      }
    });
    
    if (!skill) {
      throw new Error('Skill not found in catalog');
    }

    return await prisma.taskRequiredSkill.create({
      data: {
        taskId,
        skillName,
        requiredLevel: Math.min(Math.max(requiredLevel, 1), 5)
      }
    });
  }
  
  async removeSkillRequirementFromTask(taskId: string, skillName: string) {
    return await prisma.taskRequiredSkill.delete({
      where: {
        taskId_skillName: {
          taskId,
          skillName
        }
      }
    });
  }
  
  async getTaskSkillRequirements(taskId: string) {
    return await prisma.taskRequiredSkill.findMany({
      where: { taskId }
    });
  }
  
  // Skills catalog management
  async createSkill(organizationId: string, name: string, description?: string, category?: string) {
    // Check if skill already exists
    const existing = await prisma.skillsCatalog.findUnique({
      where: {
        organizationId_name: {
          organizationId,
          name
        }
      }
    });
    
    if (existing) {
      throw new Error('Skill already exists in catalog');
    }

    return await prisma.skillsCatalog.create({
      data: {
        organizationId,
        name,
        description,
        category,
        isActive: true
      }
    });
  }
  
  async getOrganizationSkills(organizationId: string) {
    return await prisma.skillsCatalog.findMany({
      where: {
        organizationId,
        isActive: true
      }
    });
  }
  
  // Skill matching utilities
  async findAgentsWithSkills(organizationId: string, requiredSkills: string[]) {
    return await prisma.user.findMany({
      where: {
        organizationId,
        role: 'AGENT',
        skills: {
          some: {
            skillName: {
              in: requiredSkills
            }
          }
        }
      },
      include: {
        skills: true
      }
    });
  }
  
  async calculateSkillMatch(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): Promise<number> {
    if (requiredSkills.length === 0) return 1.0; // No requirements = perfect match
    
    let totalMatch = 0;
    let totalRequired = 0;
    
    for (const required of requiredSkills) {
      const agentSkill = agentSkills.find(s => s.skillName === required.skillName);
      
      if (agentSkill) {
        // Calculate match quality based on proficiency vs requirement
        const matchQuality = Math.min(agentSkill.proficiencyLevel / required.requiredLevel, 1.0);
        totalMatch += matchQuality;
      }
      totalRequired += 1;
    }
    
    return totalMatch / totalRequired; // Returns 0-1 score
  }
}