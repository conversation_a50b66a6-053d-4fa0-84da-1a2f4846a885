import { prisma } from "./prisma";
import { SLAManager } from "./sla-manager";
import { TaskStatus } from "@prisma/client";

export interface SLAMonitorConfig {
  intervalMinutes: number;
  enableEscalations: boolean;
  enableNotifications: boolean;
  maxConcurrentProcessing: number;
}

export class SLAMonitor {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;
  private slaManager: SLAManager;
  private config: SLAMonitorConfig;

  constructor(config: Partial<SLAMonitorConfig> = {}) {
    this.slaManager = new SLAManager();
    this.config = {
      intervalMinutes: 5,
      enableEscalations: true,
      enableNotifications: true,
      maxConcurrentProcessing: 10,
      ...config,
    };
  }

  start(): void {
    if (this.isRunning) {
      console.log("SLA Monitor is already running");
      return;
    }

    console.log(
      `Starting SLA Monitor with ${this.config.intervalMinutes} minute intervals`
    );
    this.isRunning = true;

    // Run immediately on start
    this.runMonitoringCycle();

    // Set up recurring monitoring
    this.intervalId = setInterval(() => {
      this.runMonitoringCycle();
    }, this.config.intervalMinutes * 60 * 1000);
  }

  stop(): void {
    if (!this.isRunning) {
      console.log("SLA Monitor is not running");
      return;
    }

    console.log("Stopping SLA Monitor");
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  async runMonitoringCycle(): Promise<void> {
    if (!this.isRunning) return;

    try {
      console.log("Starting SLA monitoring cycle...");

      // Check for SLA breaches
      await this.checkSLABreaches();

      // Process escalations if enabled
      if (this.config.enableEscalations) {
        await this.processEscalations();
      }

      // Update SLA metrics
      await this.updateSLAMetrics();

      // Clean up old data
      await this.cleanupOldData();

      console.log("SLA monitoring cycle completed successfully");
    } catch (error) {
      console.error("Error in SLA monitoring cycle:", error);
    }
  }

  private async checkSLABreaches(): Promise<void> {
    const now = new Date();

    // Find tasks with potential SLA breaches
    const tasksWithSLA = await prisma.task.findMany({
      where: {
        status: { not: TaskStatus.COMPLETED },
        slaPolicyId: { not: null },
        OR: [
          {
            // Response time breach
            responseBy: { lt: now },
            respondedAt: null,
            breachedSLA: false,
          },
          {
            // Resolution time breach
            resolveBy: { lt: now },
            resolvedAt: null,
            breachedSLA: false,
          },
        ],
      },
      include: {
        slaPolicy: true,
        escalations: {
          where: { status: "active" },
          orderBy: { level: "desc" },
        },
      },
    });

    console.log(
      `Found ${tasksWithSLA.length} tasks with potential SLA breaches`
    );

    // Process each breached task
    for (const task of tasksWithSLA) {
      try {
        await this.handleSLABreach(task);
      } catch (error) {
        console.error(`Error handling SLA breach for task ${task.id}:`, error);
      }
    }
  }

  private async handleSLABreach(task: {
    id: string;
    responseBy: Date | null;
    resolveBy: Date | null;
    respondedAt: Date | null;
    resolvedAt: Date | null;
    escalations: Array<{ reason: string; status: string }>;
    slaPolicy?: { escalationRules: unknown };
  }): Promise<void> {
    const now = new Date();

    // Mark task as breached
    await prisma.task.update({
      where: { id: task.id },
      data: { breachedSLA: true },
    });

    // Determine breach type
    const responseBreached =
      task.responseBy && !task.respondedAt && now > task.responseBy;
    const resolutionBreached =
      task.resolveBy && !task.resolvedAt && now > task.resolveBy;

    // Create escalation if none exists for this breach type
    const existingEscalation = task.escalations.find(
      (e) =>
        e.reason === (responseBreached ? "response_time" : "resolution_time") &&
        e.status === "active"
    );

    if (!existingEscalation) {
      await this.createEscalation(
        task,
        responseBreached ? "response_time" : "resolution_time"
      );
    }

    console.log(
      `SLA breach handled for task ${task.id} (${
        responseBreached ? "response" : "resolution"
      } time)`
    );
  }

  private async createEscalation(
    task: { id: string; slaPolicy?: { escalationRules: unknown } },
    reason: string
  ): Promise<void> {
    // Determine escalation level
    const existingEscalations = await prisma.escalation.count({
      where: { taskId: task.id },
    });

    const level = existingEscalations + 1;

    // Create escalation record
    const escalation = await prisma.escalation.create({
      data: {
        taskId: task.id,
        level,
        reason,
        status: "active",
        escalatedTo: null, // Will be determined by escalation rules
        comment: `Automatic escalation due to ${reason} breach`,
      },
    });

    // Execute escalation actions based on SLA policy rules
    if (task.slaPolicy?.escalationRules) {
      await this.executeEscalationActions(
        task,
        escalation,
        task.slaPolicy.escalationRules
      );
    }

    console.log(`Created escalation level ${level} for task ${task.id}`);
  }

  private async executeEscalationActions(
    task: { id: string },
    escalation: { id: string; level: number },
    escalationRules: Array<{
      level: number;
      actions?: Array<{ type: string; assignTo?: string; priority?: string }>;
    }>
  ): Promise<void> {
    const rule = escalationRules.find((r) => r.level === escalation.level);
    if (!rule) return;

    for (const action of rule.actions || []) {
      try {
        switch (action.type) {
          case "reassign":
            if (action.assignTo) {
              await prisma.task.update({
                where: { id: task.id },
                data: { assignedTo: action.assignTo },
              });

              await prisma.escalation.update({
                where: { id: escalation.id },
                data: { escalatedTo: action.assignTo },
              });
            }
            break;

          case "update_priority":
            if (action.priority) {
              await prisma.task.update({
                where: { id: task.id },
                data: { priority: action.priority },
              });
            }
            break;

          case "notify":
            // TODO: Implement notification system
            console.log(
              `Notification action for task ${task.id} (not implemented)`
            );
            break;

          default:
            console.warn(`Unknown escalation action type: ${action.type}`);
        }
      } catch (error) {
        console.error(
          `Error executing escalation action ${action.type}:`,
          error
        );
      }
    }
  }

  private async processEscalations(): Promise<void> {
    // Process any pending escalations
    await this.slaManager.processEscalations();
  }

  private async updateSLAMetrics(): Promise<void> {
    // Get all organizations and update their SLA metrics
    const organizations = await prisma.organization.findMany({
      select: { id: true },
    });

    for (const org of organizations) {
      try {
        const metrics = await this.slaManager.getSLAMetrics(org.id);
        // Store metrics in cache or database if needed
        console.log(`Updated SLA metrics for organization ${org.id}:`, {
          compliance: metrics.responseCompliance,
          escalations: metrics.escalationRate,
        });
      } catch (error) {
        console.error(
          `Error updating SLA metrics for organization ${org.id}:`,
          error
        );
      }
    }
  }

  private async cleanupOldData(): Promise<void> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Clean up old resolved escalations
    const deletedEscalations = await prisma.escalation.deleteMany({
      where: {
        status: "resolved",
        resolvedAt: { lt: thirtyDaysAgo },
      },
    });

    if (deletedEscalations.count > 0) {
      console.log(
        `Cleaned up ${deletedEscalations.count} old escalation records`
      );
    }
  }

  // Manual escalation trigger
  async triggerManualEscalation(
    taskId: string,
    reason: string,
    escalatedBy: string
  ): Promise<void> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: { escalations: true },
    });

    if (!task) {
      throw new Error("Task not found");
    }

    const level = task.escalations.length + 1;

    await prisma.escalation.create({
      data: {
        taskId,
        level,
        reason: "manual",
        status: "active",
        comment: reason,
      },
    });

    console.log(
      `Manual escalation created for task ${taskId} by ${escalatedBy}`
    );
  }

  // Get monitoring status
  getStatus(): { isRunning: boolean; config: SLAMonitorConfig } {
    return {
      isRunning: this.isRunning,
      config: this.config,
    };
  }
}

// Singleton instance for global use
export const slaMonitor = new SLAMonitor();
