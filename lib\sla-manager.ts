import { prisma } from "./prisma";
import { Task, TaskStatus, TaskPriority } from "@prisma/client";
import { AvailabilityManager } from "./availability-utils";

// SLA Types
export interface SLAPolicy {
  id: string;
  organizationId: string;
  name: string;
  description?: string | null;
  priority: string;
  responseTime: number;
  resolutionTime: number;
  escalationRules: EscalationRule[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EscalationRule {
  level: number;
  escalateTo?: string;
  actions: EscalationAction[];
}

export interface EscalationAction {
  type: "notify" | "reassign" | "update_priority";
  assignTo?: string;
  priority?: TaskPriority;
  notifyUsers?: string[];
}

export interface SLAStatus {
  taskId: string;
  responseBreached: boolean;
  resolutionBreached: boolean;
  currentEscalationLevel: number;
  breachedSLA: boolean;
  responseRemaining: number | null;
  resolutionRemaining: number | null;
}

export class SLAManager {
  private availabilityManager: AvailabilityManager;

  constructor() {
    this.availabilityManager = new AvailabilityManager();
  }

  // SLA Policy Management
  async applySLAPolicy(taskId: string, policyId: string): Promise<Task> {
    const policy = await prisma.sLAPolicy.findUnique({
      where: { id: policyId },
    });

    if (!policy) {
      throw new Error("SLA Policy not found");
    }

    // Calculate deadlines based on policy
    const now = new Date();
    const responseBy = this.calculateDeadline(now, policy.responseTime);
    const resolveBy = this.calculateDeadline(now, policy.resolutionTime);

    // Apply SLA policy to task
    return await prisma.task.update({
      where: { id: taskId },
      data: {
        slaPolicyId: policyId,
        responseBy,
        resolveBy,
        priority: policy.priority as TaskPriority,
      },
    });
  }

  // Deadline Calculations
  private calculateDeadline(startTime: Date, minutes: number): Date {
    const deadline = new Date(startTime);
    const remainingMinutes = minutes;

    // TODO: Consider working hours and holidays using availabilityManager
    // For now, just add the minutes directly
    deadline.setMinutes(deadline.getMinutes() + remainingMinutes);
    return deadline;
  }

  // SLA Status Check
  async checkSLAStatus(taskId: string): Promise<SLAStatus> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        slaPolicy: true,
        escalations: {
          where: { status: "active" },
          orderBy: { level: "desc" },
          take: 1,
        },
      },
    });

    if (!task || !task.slaPolicy) {
      throw new Error("Task or SLA policy not found");
    }

    const now = new Date();
    const responseBreached =
      task.responseBy && !task.respondedAt && now > task.responseBy;
    const resolutionBreached =
      task.resolveBy && !task.resolvedAt && now > task.resolveBy;

    // Update SLA breach status if needed
    if ((responseBreached || resolutionBreached) && !task.breachedSLA) {
      await prisma.task.update({
        where: { id: taskId },
        data: { breachedSLA: true },
      });
    }

    return {
      taskId: task.id,
      responseBreached: responseBreached ?? false,
      resolutionBreached: resolutionBreached ?? false,
      currentEscalationLevel: task.escalations[0]?.level || 0,
      breachedSLA: (responseBreached ?? false) || (resolutionBreached ?? false),
      responseRemaining: task.responseBy
        ? this.calculateTimeRemaining(now, task.responseBy)
        : null,
      resolutionRemaining: task.resolveBy
        ? this.calculateTimeRemaining(now, task.resolveBy)
        : null,
    };
  }

  // Escalation Management
  async processEscalations(): Promise<void> {
    const tasks = await prisma.task.findMany({
      where: {
        status: { not: TaskStatus.COMPLETED },
        slaPolicy: { isNot: null },
      },
      include: {
        slaPolicy: true,
        escalations: {
          where: { status: "active" },
          orderBy: { level: "desc" },
          take: 1,
        },
      },
    });

    for (const task of tasks) {
      try {
        const status = await this.checkSLAStatus(task.id);

        if (status.breachedSLA) {
          await this.handleSLABreach(task, status);
        }
      } catch (error) {
        console.error(
          `Error processing escalations for task ${task.id}:`,
          error
        );
      }
    }
  }

  private async handleSLABreach(task: Task, status: SLAStatus): Promise<void> {
    if (!task.slaPolicyId) return;

    const policy = await prisma.sLAPolicy.findUnique({
      where: { id: task.slaPolicyId },
    });

    if (!policy) return;

    const escalationRules =
      policy.escalationRules as unknown as EscalationRule[];
    const currentLevel = status.currentEscalationLevel;

    // Find next applicable escalation rule
    const nextRule = escalationRules.find(
      (rule) => rule.level === currentLevel + 1
    );
    if (!nextRule) return;

    // Create new escalation
    const escalation = await prisma.escalation.create({
      data: {
        taskId: task.id,
        level: nextRule.level,
        reason: status.responseBreached ? "response_time" : "resolution_time",
        status: "active",
        escalatedTo: nextRule.escalateTo,
      },
    });

    // Execute escalation actions
    await this.executeEscalationActions(task, escalation, nextRule.actions);
  }

  private async executeEscalationActions(
    task: Task,
    escalation: { id: string; taskId: string; level: number; status: string },
    actions: EscalationAction[]
  ): Promise<void> {
    for (const action of actions) {
      try {
        switch (action.type) {
          case "notify":
            // TODO: Implement notification system integration
            break;

          case "reassign":
            if (action.assignTo) {
              await prisma.task.update({
                where: { id: task.id },
                data: { assignedTo: action.assignTo },
              });
            }
            break;

          case "update_priority":
            if (action.priority) {
              await prisma.task.update({
                where: { id: task.id },
                data: { priority: action.priority },
              });
            }
            break;

          default:
            console.warn(`Unknown escalation action type: ${action.type}`);
        }
      } catch (error) {
        console.error(`Error executing escalation action:`, error);
      }
    }
  }

  // Helper Methods
  private calculateTimeRemaining(now: Date, deadline: Date): number {
    return Math.max(0, deadline.getTime() - now.getTime()) / 1000 / 60; // minutes
  }

  // Resolution Management
  async markResponded(taskId: string): Promise<Task> {
    return await prisma.task.update({
      where: { id: taskId },
      data: {
        respondedAt: new Date(),
        // Close any response time escalations
        escalations: {
          updateMany: {
            where: {
              status: "active",
              reason: "response_time",
            },
            data: {
              status: "resolved",
              resolvedAt: new Date(),
            },
          },
        },
      },
    });
  }

  async markResolved(taskId: string): Promise<Task> {
    return await prisma.task.update({
      where: { id: taskId },
      data: {
        resolvedAt: new Date(),
        status: TaskStatus.COMPLETED,
        // Close all active escalations
        escalations: {
          updateMany: {
            where: { status: "active" },
            data: {
              status: "resolved",
              resolvedAt: new Date(),
            },
          },
        },
      },
    });
  }

  // SLA Metrics and Reporting
  async getSLAMetrics(
    organizationId: string,
    timeframe: "day" | "week" | "month" = "month"
  ): Promise<{
    totalTasks: number;
    responseCompliance: number;
    resolutionCompliance: number;
    escalationRate: number;
    avgResponseTime: number;
    avgResolutionTime: number;
    responseBreaches: number;
    resolutionBreaches: number;
    escalatedTasks: number;
    timeframe: string;
  }> {
    const now = new Date();
    let startDate: Date;

    switch (timeframe) {
      case "day":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
    }

    const tasks = await prisma.task.findMany({
      where: {
        organizationId,
        createdAt: { gte: startDate },
        slaPolicyId: { not: null },
      },
      include: {
        slaPolicy: true,
        escalations: true,
      },
    });

    const totalTasks = tasks.length;
    const responseBreaches = tasks.filter(
      (t) => t.responseBy && !t.respondedAt && now > t.responseBy
    ).length;
    const resolutionBreaches = tasks.filter(
      (t) => t.resolveBy && !t.resolvedAt && now > t.resolveBy
    ).length;
    const escalatedTasks = tasks.filter((t) => t.escalations.length > 0).length;

    // Calculate average response and resolution times
    const respondedTasks = tasks.filter((t) => t.respondedAt && t.responseBy);
    const resolvedTasks = tasks.filter((t) => t.resolvedAt && t.resolveBy);

    const avgResponseTime =
      respondedTasks.length > 0
        ? respondedTasks.reduce((sum, task) => {
            const responseTime =
              task.respondedAt!.getTime() - task.createdAt.getTime();
            return sum + responseTime;
          }, 0) /
          respondedTasks.length /
          (1000 * 60) // Convert to minutes
        : 0;

    const avgResolutionTime =
      resolvedTasks.length > 0
        ? resolvedTasks.reduce((sum, task) => {
            const resolutionTime =
              task.resolvedAt!.getTime() - task.createdAt.getTime();
            return sum + resolutionTime;
          }, 0) /
          resolvedTasks.length /
          (1000 * 60) // Convert to minutes
        : 0;

    return {
      totalTasks,
      responseCompliance:
        totalTasks > 0
          ? ((totalTasks - responseBreaches) / totalTasks) * 100
          : 100,
      resolutionCompliance:
        totalTasks > 0
          ? ((totalTasks - resolutionBreaches) / totalTasks) * 100
          : 100,
      escalationRate: totalTasks > 0 ? (escalatedTasks / totalTasks) * 100 : 0,
      avgResponseTime: Math.round(avgResponseTime),
      avgResolutionTime: Math.round(avgResolutionTime),
      responseBreaches,
      resolutionBreaches,
      escalatedTasks,
      timeframe,
    };
  }

  // Get applicable SLA policy for a task
  async getApplicableSLAPolicy(task: Task): Promise<SLAPolicy | null> {
    const policies = await prisma.sLAPolicy.findMany({
      where: {
        organizationId: task.organizationId,
        isActive: true,
      },
      orderBy: [
        { priority: "desc" }, // Higher priority policies first
        { createdAt: "desc" },
      ],
    });

    // Find the most specific policy that matches the task
    for (const policy of policies) {
      // Check if policy matches task type and priority
      const matchesType = !policy.priority || policy.priority === task.priority;

      if (matchesType) {
        return policy as SLAPolicy;
      }
    }

    // Return default policy if no specific match
    const defaultPolicy = policies.find((p) => !p.priority);
    return (defaultPolicy as SLAPolicy) || null;
  }

  // Create SLA tracking for a new task
  async createTaskSLA(task: Task): Promise<void> {
    const policy = await this.getApplicableSLAPolicy(task);
    if (!policy) return;

    const now = new Date();
    const responseBy = new Date(
      now.getTime() + policy.responseTime * 60 * 1000
    );
    const resolveBy = new Date(
      now.getTime() + policy.resolutionTime * 60 * 1000
    );

    await prisma.task.update({
      where: { id: task.id },
      data: {
        slaPolicyId: policy.id,
        responseBy,
        resolveBy,
      },
    });
  }
}
