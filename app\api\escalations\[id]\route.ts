import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

interface Props {
  params: {
    id: string;
  };
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const escalationId = params.id;

    // Get escalation with task details
    const escalation = await prisma.escalation.findFirst({
      where: {
        id: escalationId,
        task: {
          organization: {
            users: {
              some: {
                id: session.user.id,
              },
            },
          },
        },
      },
      include: {
        task: {
          include: {
            assignedUser: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            slaPolicy: true,
          },
        },
      },
    });

    if (!escalation) {
      return NextResponse.json(
        { error: "Escalation not found or access denied" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      escalation,
    });
  } catch (error) {
    console.error("Error in GET /api/escalations/[id]:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const escalationId = params.id;

    // Parse request body
    const body = await req.json();
    const { status, escalatedTo, comment } = body;

    // Validate status
    if (status && !["pending", "active", "resolved"].includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be pending, active, or resolved" },
        { status: 400 }
      );
    }

    // Verify escalation exists and user has access
    const escalation = await prisma.escalation.findFirst({
      where: {
        id: escalationId,
        task: {
          organization: {
            users: {
              some: {
                id: session.user.id,
              },
            },
          },
        },
      },
    });

    if (!escalation) {
      return NextResponse.json(
        { error: "Escalation not found or access denied" },
        { status: 404 }
      );
    }

    // Build update data
    const updateData: {
      status?: string;
      escalatedTo?: string;
      comment?: string;
      resolvedAt?: Date;
    } = {};

    if (status !== undefined) {
      updateData.status = status;
      if (status === "resolved") {
        updateData.resolvedAt = new Date();
      }
    }

    if (escalatedTo !== undefined) {
      updateData.escalatedTo = escalatedTo;
    }

    if (comment !== undefined) {
      updateData.comment = comment;
    }

    // Update escalation
    const updatedEscalation = await prisma.escalation.update({
      where: { id: escalationId },
      data: updateData,
      include: {
        task: {
          include: {
            assignedUser: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      escalation: updatedEscalation,
    });
  } catch (error) {
    console.error("Error in PATCH /api/escalations/[id]:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const escalationId = params.id;

    // Verify escalation exists and user has access
    const escalation = await prisma.escalation.findFirst({
      where: {
        id: escalationId,
        task: {
          organization: {
            users: {
              some: {
                id: session.user.id,
              },
            },
          },
        },
      },
    });

    if (!escalation) {
      return NextResponse.json(
        { error: "Escalation not found or access denied" },
        { status: 404 }
      );
    }

    // Delete escalation
    await prisma.escalation.delete({
      where: { id: escalationId },
    });

    return NextResponse.json({
      success: true,
      message: "Escalation deleted successfully",
    });
  } catch (error) {
    console.error("Error in DELETE /api/escalations/[id]:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
