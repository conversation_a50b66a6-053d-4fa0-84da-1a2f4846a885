import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface Props {
  params: {
    id: string;
  };
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const policyId = params.id;

    // Get SLA policy
    const policy = await prisma.sLAPolicy.findFirst({
      where: {
        id: policyId,
        organization: {
          users: {
            some: {
              id: session.user.id
            }
          }
        }
      },
      include: {
        tasks: {
          select: {
            id: true,
            title: true,
            status: true,
            breachedSLA: true
          },
          take: 10,
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            tasks: true
          }
        }
      }
    });

    if (!policy) {
      return NextResponse.json(
        { error: 'SLA policy not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      policy
    });
  } catch (error) {
    console.error('Error in GET /api/sla/policies/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const policyId = params.id;

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      priority,
      responseTime,
      resolutionTime,
      escalationRules,
      isActive
    } = body;

    // Verify policy exists and user has access
    const existingPolicy = await prisma.sLAPolicy.findFirst({
      where: {
        id: policyId,
        organization: {
          users: {
            some: {
              id: session.user.id
            }
          }
        }
      }
    });

    if (!existingPolicy) {
      return NextResponse.json(
        { error: 'SLA policy not found or access denied' },
        { status: 404 }
      );
    }

    // Build update data
    const updateData: any = {};

    if (name !== undefined) {
      // Check for duplicate name
      const duplicatePolicy = await prisma.sLAPolicy.findFirst({
        where: {
          organizationId: existingPolicy.organizationId,
          name,
          id: { not: policyId }
        }
      });

      if (duplicatePolicy) {
        return NextResponse.json(
          { error: 'SLA policy with this name already exists' },
          { status: 409 }
        );
      }

      updateData.name = name;
    }

    if (description !== undefined) updateData.description = description;
    if (priority !== undefined) {
      if (!['low', 'medium', 'high', 'urgent'].includes(priority)) {
        return NextResponse.json(
          { error: 'Priority must be one of: low, medium, high, urgent' },
          { status: 400 }
        );
      }
      updateData.priority = priority;
    }
    if (responseTime !== undefined) {
      if (responseTime <= 0) {
        return NextResponse.json(
          { error: 'Response time must be a positive number' },
          { status: 400 }
        );
      }
      updateData.responseTime = responseTime;
    }
    if (resolutionTime !== undefined) {
      if (resolutionTime <= 0) {
        return NextResponse.json(
          { error: 'Resolution time must be a positive number' },
          { status: 400 }
        );
      }
      updateData.resolutionTime = resolutionTime;
    }
    if (escalationRules !== undefined) updateData.escalationRules = escalationRules;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update policy
    const updatedPolicy = await prisma.sLAPolicy.update({
      where: { id: policyId },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      policy: updatedPolicy
    });
  } catch (error) {
    console.error('Error in PATCH /api/sla/policies/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const policyId = params.id;

    // Verify policy exists and user has access
    const policy = await prisma.sLAPolicy.findFirst({
      where: {
        id: policyId,
        organization: {
          users: {
            some: {
              id: session.user.id
            }
          }
        }
      },
      include: {
        _count: {
          select: {
            tasks: true
          }
        }
      }
    });

    if (!policy) {
      return NextResponse.json(
        { error: 'SLA policy not found or access denied' },
        { status: 404 }
      );
    }

    // Check if policy is in use
    if (policy._count.tasks > 0) {
      return NextResponse.json(
        { error: 'Cannot delete SLA policy that is in use by tasks' },
        { status: 409 }
      );
    }

    // Delete policy
    await prisma.sLAPolicy.delete({
      where: { id: policyId }
    });

    return NextResponse.json({
      success: true,
      message: 'SLA policy deleted successfully'
    });
  } catch (error) {
    console.error('Error in DELETE /api/sla/policies/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
