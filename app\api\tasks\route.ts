import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { createTask, getDemoOrgId } from "@/lib/db-utils";
import { autoAssignTask } from "@/lib/task-router";

import { TaskStatus, TaskPriority } from "@prisma/client";
import { SLAManager } from "@/lib/sla-manager";

// GET /api/tasks - List tasks with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Get query parameters
    const organizationId =
      searchParams.get("organizationId") || (await getDemoOrgId());
    const assignedTo = searchParams.get("assignedTo");
    const status = searchParams.get("status") as TaskStatus | null;
    const priority = searchParams.get("priority") as TaskPriority | null;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = (page - 1) * limit;

    // Build where clause
    const where: {
      organizationId: string;
      assignedTo?: string;
      status?: TaskStatus;
      priority?: TaskPriority;
    } = {
      organizationId,
    };

    if (assignedTo) {
      where.assignedTo = assignedTo;
    }

    if (status) {
      where.status = status;
    }

    if (priority) {
      where.priority = priority;
    }

    // Get tasks with pagination
    const [tasks, totalCount] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          assignedUser: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              events: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: offset,
        take: limit,
      }),
      prisma.task.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        tasks,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "FETCH_TASKS_FAILED",
          message: "Failed to fetch tasks",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      },
      { status: 500 }
    );
  }
}

// POST /api/tasks - Create new task
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "Title is required",
          },
        },
        { status: 400 }
      );
    }

    // Get organization ID (for now using demo org, later from auth)
    const organizationId = await getDemoOrgId();

    // Create task using utility function
    const task = await createTask(organizationId, {
      title: body.title,
      description: body.description,
      priority: body.priority || TaskPriority.MEDIUM,
      type: body.type || "general",
      estimatedDuration: body.estimatedDuration,
      source: body.source || "manual",
      responseDeadline: body.responseDeadline,
      slaDeadline: body.slaDeadline,
    });

    // Apply SLA policy to the task
    const slaManager = new SLAManager();
    await slaManager.createTaskSLA(task);

    // Attempt automatic assignment using round-robin routing
    const routingResult = await autoAssignTask(
      task.id,
      organizationId,
      "round_robin"
    );

    // Get updated task with assignment info and SLA details
    const finalTask = await prisma.task.findUnique({
      where: { id: task.id },
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        slaPolicy: {
          select: {
            id: true,
            name: true,
            responseTime: true,
            resolutionTime: true,
          },
        },
      },
    });

    // Prepare response message
    let message = "Task created successfully";
    if (routingResult.success) {
      message += ` and assigned to ${routingResult.assignedAgent?.name}`;
    } else if (routingResult.reason) {
      message += ` but not assigned: ${routingResult.reason}`;
    }

    return NextResponse.json(
      {
        success: true,
        data: finalTask,
        message,
        routing: {
          attempted: true,
          success: routingResult.success,
          reason: routingResult.reason || routingResult.error,
          assignedAgent: routingResult.assignedAgent?.name,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating task:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "CREATE_TASK_FAILED",
          message: "Failed to create task",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      },
      { status: 500 }
    );
  }
}
