import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');
    const isActive = searchParams.get('isActive');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Build where clause
    const where: any = { organizationId };
    
    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    // Get SLA policies
    const policies = await prisma.sLAPolicy.findMany({
      where,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json({
      success: true,
      policies
    });
  } catch (error) {
    console.error('Error in GET /api/sla/policies:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const {
      organizationId,
      name,
      description,
      priority,
      responseTime,
      resolutionTime,
      escalationRules,
      isActive = true
    } = body;

    // Validate required fields
    if (!organizationId || !name || !priority || !responseTime || !resolutionTime) {
      return NextResponse.json(
        { error: 'Missing required fields: organizationId, name, priority, responseTime, resolutionTime' },
        { status: 400 }
      );
    }

    // Validate numeric fields
    if (responseTime <= 0 || resolutionTime <= 0) {
      return NextResponse.json(
        { error: 'Response time and resolution time must be positive numbers' },
        { status: 400 }
      );
    }

    // Validate priority
    if (!['low', 'medium', 'high', 'urgent'].includes(priority)) {
      return NextResponse.json(
        { error: 'Priority must be one of: low, medium, high, urgent' },
        { status: 400 }
      );
    }

    // Validate escalation rules if provided
    if (escalationRules && Array.isArray(escalationRules)) {
      for (const rule of escalationRules) {
        if (!rule.level || !rule.actions || !Array.isArray(rule.actions)) {
          return NextResponse.json(
            { error: 'Invalid escalation rule format' },
            { status: 400 }
          );
        }
      }
    }

    // Check for duplicate policy name
    const existingPolicy = await prisma.sLAPolicy.findFirst({
      where: {
        organizationId,
        name
      }
    });

    if (existingPolicy) {
      return NextResponse.json(
        { error: 'SLA policy with this name already exists' },
        { status: 409 }
      );
    }

    // Create SLA policy
    const policy = await prisma.sLAPolicy.create({
      data: {
        organizationId,
        name,
        description,
        priority,
        responseTime,
        resolutionTime,
        escalationRules: escalationRules || [],
        isActive
      }
    });

    return NextResponse.json({
      success: true,
      policy
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/sla/policies:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
