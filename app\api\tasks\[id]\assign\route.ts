import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { EnhancedTaskRouter } from '@/lib/enhanced-task-router';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { TaskMetadata } from '@/lib/types';
import { TaskStatus, TaskEventType, Prisma } from '@prisma/client';

const router = new EnhancedTaskRouter();

interface Props {
  params: { id: string }
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get strategy from request body (optional)
    const body = await req.json();
    const { strategy } = body;

    // Get task with required skills
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        requiredSkills: true
      }
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    if (task.assignedTo) {
      return NextResponse.json(
        { error: 'Task is already assigned' },
        { status: 400 }
      );
    }

    // Route task using enhanced router
    const result = await router.routeTask({
      task,
      strategy
    });

    const metadata: TaskMetadata = {
      ...task.metadata as TaskMetadata,
      assignmentDetails: {
        confidence: result.confidence,
        reasoning: result.reasoning,
        strategy: result.appliedStrategy || strategy || 'weighted_round_robin',
        alternativeAgents: result.alternativeAgents.map(a => a.id),
      },
      appliedRules: result.ruleResults?.map(r => ({
        ruleId: r.ruleId,
        ruleName: r.ruleName,
        succeeded: r.succeeded,
        conditionsMatched: r.conditionsMatched,
        actionsExecuted: r.actionsExecuted
      }))
    };

    // Update task assignment
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedTo: result.selectedAgent.id,
        assignedAt: new Date(),
        status: TaskStatus.ASSIGNED,
        metadata: metadata as Prisma.InputJsonValue
      },
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            currentTaskCount: true
          }
        }
      }
    });

    // Update agent's task count
    await prisma.user.update({
      where: { id: result.selectedAgent.id },
      data: {
        currentTaskCount: {
          increment: 1
        }
      }
    });

    // Format rule results for event data
    const eventRuleResults = result.ruleResults?.map(r => ({
      ruleId: r.ruleId,
      ruleName: r.ruleName,
      succeeded: r.succeeded,
      conditionsMatched: r.conditionsMatched,
      actionsExecuted: r.actionsExecuted
    }));

    // Create task event for assignment
    await prisma.taskEvent.create({
      data: {
        taskId,
        type: TaskEventType.ASSIGNED,
        userId: result.selectedAgent.id,
        data: {
          confidence: result.confidence,
          reasoning: result.reasoning,
          strategy: result.appliedStrategy || strategy || 'weighted_round_robin',
          ruleResults: eventRuleResults || []
        } as Prisma.InputJsonValue
      }
    });

    return NextResponse.json({
      task: updatedTask,
      assignment: {
        agent: result.selectedAgent,
        confidence: result.confidence,
        reasoning: result.reasoning,
        alternativeAgents: result.alternativeAgents,
        strategy: result.appliedStrategy || strategy || 'weighted_round_robin',
        ruleResults: result.ruleResults
      }
    });
  } catch (error) {
    console.error('Error in POST /api/tasks/[id]/assign:', error);
    
    if (error instanceof Error && error.message === 'No eligible agents available') {
      return NextResponse.json(
        { error: 'No eligible agents available for assignment' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get task
    const task = await prisma.task.findUnique({
      where: { id: taskId }
    });

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    if (!task.assignedTo) {
      return NextResponse.json(
        { error: 'Task is not assigned' },
        { status: 400 }
      );
    }

    const metadata: TaskMetadata = {
      ...task.metadata as TaskMetadata,
      unassignmentDetails: {
        unassignedAt: new Date(),
        reason: 'manual_unassignment',
        previousAgent: task.assignedTo
      }
    };

    // Update task
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedTo: null,
        assignedAt: null,
        status: TaskStatus.PENDING,
        metadata: metadata as Prisma.InputJsonValue
      }
    });

    // Update agent's task count
    await prisma.user.update({
      where: { id: task.assignedTo },
      data: {
        currentTaskCount: {
          decrement: 1
        }
      }
    });

    // Create task event for unassignment
    await prisma.taskEvent.create({
      data: {
        taskId,
        type: TaskEventType.STATUS_CHANGED,
        userId: task.assignedTo,
        data: {
          status: TaskStatus.PENDING,
          reason: 'manual_unassignment'
        }
      }
    });

    return NextResponse.json({ task: updatedTask });
  } catch (error) {
    console.error('Error in DELETE /api/tasks/[id]/assign:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Enhanced routing function
async function performEnhancedRouting(taskId: string, organizationId: string, strategy: EnhancedRoutingStrategy) {
  try {
    // Get task with skills
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        requiredSkills: true,
        organization: true
      }
    }) as TaskWithSkills | null

    if (!task) {
      return { success: false, error: 'Task not found' }
    }

    // Get all agents with skills and performance
    const allAgents = await prisma.user.findMany({
      where: {
        organizationId,
        status: UserStatus.AVAILABLE,
        currentTaskCount: {
          lt: prisma.user.fields.maxConcurrentTasks
        }
      },
      include: {
        skills: true,
        performance: true
      }
    }) as AgentWithSkills[]

    if (allAgents.length === 0) {
      return { success: false, error: 'No agents in organization' }
    }

    // Filter agents by working hours and availability
    const { createWorkingHoursManager } = await import('@/lib/working-hours-manager')
    const workingHoursManager = createWorkingHoursManager()

    const availableAgents: AgentWithSkills[] = []

    for (const agent of allAgents) {
      // Check if agent is available considering working hours, availability status, and capacity
      const isAvailable = await workingHoursManager.isAgentAvailable(agent.id)

      if (isAvailable) {
        availableAgents.push(agent)
      }
    }

    // Handle urgent tasks override
    if (availableAgents.length === 0 && task.priority === 'URGENT') {
      const orgSettings = await workingHoursManager.getOrganizationSettings(organizationId)

      if (orgSettings.urgentTasksOverride) {
        // For urgent tasks, include agents even if outside working hours
        // but still respect availability status and capacity
        for (const agent of allAgents) {
          const availability = await workingHoursManager.getAgentAvailability(agent.id)

          if (!availability || availability.status === 'available') {
            availableAgents.push(agent)
          }
        }
      }
    }

    if (availableAgents.length === 0) {
      return { success: false, error: 'No available agents considering working hours and availability' }
    }

    // Create routing context
    const context: RoutingContext = {
      task,
      availableAgents,
      organizationId,
      strategy
    }

    // Use enhanced router
    const enhancedRouter = createEnhancedTaskRouter(organizationId)
    const routingResult = await enhancedRouter.routeTask(context)

    // Assign the task
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedTo: routingResult.selectedAgent.id,
        assignedAt: new Date(),
        status: 'ASSIGNED'
      },
      include: {
        assignedUser: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // Update agent task count
    await prisma.user.update({
      where: { id: routingResult.selectedAgent.id },
      data: {
        currentTaskCount: {
          increment: 1
        }
      }
    })

    return {
      success: true,
      task: updatedTask,
      assignedAgent: routingResult.selectedAgent,
      confidence: routingResult.confidence,
      reasoning: routingResult.reasoning,
      alternativeAgents: routingResult.alternativeAgents
    }

  } catch (error) {
    console.error('Enhanced routing error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Enhanced routing failed'
    }
  }
}
