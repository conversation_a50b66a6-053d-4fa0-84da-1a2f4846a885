import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getDemoOrgId } from "@/lib/db-utils";
import type { EnhancedRoutingStrategy } from "@/lib/types";

// GET /api/routing/strategies/[id] - Get specific routing strategy
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const organizationId = await getDemoOrgId();

    const strategy = await prisma.routingStrategy.findFirst({
      where: {
        id: params.id,
        organizationId: organizationId,
      },
    });

    if (!strategy) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "STRATEGY_NOT_FOUND",
            message: "Routing strategy not found",
          },
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: strategy,
      message: "Routing strategy retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching routing strategy:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "FETCH_STRATEGY_FAILED",
          message: "Failed to fetch routing strategy",
        },
      },
      { status: 500 }
    );
  }
}

// PATCH /api/routing/strategies/[id] - Update routing strategy
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, strategy, configuration, isDefault, isActive } = body;

    const organizationId = await getDemoOrgId();

    // Check if strategy exists
    const existingStrategy = await prisma.routingStrategy.findFirst({
      where: {
        id: params.id,
        organizationId: organizationId,
      },
    });

    if (!existingStrategy) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "STRATEGY_NOT_FOUND",
            message: "Routing strategy not found",
          },
        },
        { status: 404 }
      );
    }

    // Validate strategy type if provided
    if (strategy) {
      const validStrategies: EnhancedRoutingStrategy[] = [
        "weighted_round_robin",
        "best_skill_match",
        "performance_based",
        "hybrid_intelligent",
      ];

      if (!validStrategies.includes(strategy)) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: "INVALID_STRATEGY",
              message: `Invalid strategy. Must be one of: ${validStrategies.join(
                ", "
              )}`,
            },
          },
          { status: 400 }
        );
      }
    }

    // Check if name already exists (if changing name)
    if (name && name !== existingStrategy.name) {
      const nameExists = await prisma.routingStrategy.findFirst({
        where: {
          organizationId: organizationId,
          name: name,
          id: { not: params.id },
        },
      });

      if (nameExists) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: "STRATEGY_NAME_EXISTS",
              message: "A strategy with this name already exists",
            },
          },
          { status: 409 }
        );
      }
    }

    // If setting as default, unset other defaults
    if (isDefault === true) {
      await prisma.routingStrategy.updateMany({
        where: {
          organizationId: organizationId,
          isDefault: true,
          id: { not: params.id },
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Update the strategy
    const updatedStrategy = await prisma.routingStrategy.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(strategy && { strategy }),
        ...(configuration !== undefined && { configuration }),
        ...(isDefault !== undefined && { is_default: isDefault }),
        ...(isActive !== undefined && { is_active: isActive }),
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedStrategy,
      message: "Routing strategy updated successfully",
    });
  } catch (error) {
    console.error("Error updating routing strategy:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "UPDATE_STRATEGY_FAILED",
          message: "Failed to update routing strategy",
        },
      },
      { status: 500 }
    );
  }
}

// DELETE /api/routing/strategies/[id] - Delete routing strategy
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const organizationId = await getDemoOrgId();

    // Check if strategy exists
    const existingStrategy = await prisma.routingStrategy.findFirst({
      where: {
        id: params.id,
        organizationId: organizationId,
      },
    });

    if (!existingStrategy) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "STRATEGY_NOT_FOUND",
            message: "Routing strategy not found",
          },
        },
        { status: 404 }
      );
    }

    // Don't allow deletion of default strategy if it's the only one
    if (existingStrategy.isDefault) {
      const otherStrategies = await prisma.routingStrategy.count({
        where: {
          organizationId: organizationId,
          isActive: true,
          id: { not: params.id },
        },
      });

      if (otherStrategies === 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: "CANNOT_DELETE_LAST_STRATEGY",
              message: "Cannot delete the only active routing strategy",
            },
          },
          { status: 400 }
        );
      }

      // Set another strategy as default
      const nextStrategy = await prisma.routingStrategy.findFirst({
        where: {
          organizationId: organizationId,
          isActive: true,
          id: { not: params.id },
        },
      });

      if (nextStrategy) {
        await prisma.routingStrategy.update({
          where: { id: nextStrategy.id },
          data: { isDefault: true },
        });
      }
    }

    // Delete the strategy
    await prisma.routingStrategy.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Routing strategy deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting routing strategy:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "DELETE_STRATEGY_FAILED",
          message: "Failed to delete routing strategy",
        },
      },
      { status: 500 }
    );
  }
}
