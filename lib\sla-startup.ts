import { slaMonitor } from './sla-monitor';

// SLA Monitor startup configuration
export function startSLAMonitor() {
  if (process.env.NODE_ENV === 'production' || process.env.ENABLE_SLA_MONITOR === 'true') {
    console.log('🔍 Starting SLA Monitor...');
    
    // Start the SLA monitor with production settings
    slaMonitor.start();
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('🛑 Shutting down SLA Monitor...');
      slaMonitor.stop();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('🛑 Shutting down SLA Monitor...');
      slaMonitor.stop();
      process.exit(0);
    });

    console.log('✅ SLA Monitor started successfully');
  } else {
    console.log('⏸️ SLA Monitor disabled (set ENABLE_SLA_MONITOR=true to enable in development)');
  }
}

// Export for manual control
export { slaMonitor };
