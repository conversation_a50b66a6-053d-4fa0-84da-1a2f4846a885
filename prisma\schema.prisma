generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users User[]
  tasks Task[]
  skillsCatalog SkillsCatalog[]
  routingStrategies RoutingStrategy[]
  routingRules RoutingRule[]
  slaPolicies SLAPolicy[]
  teamMetrics TeamMetrics[]
  performanceMetrics PerformanceMetric[]
  systemMetrics SystemMetric[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  name           String
  email          String   @unique
  password       String?  // For credential-based auth
  role           UserRole @default(AGENT)
  organizationId String

  // Basic agent properties for Phase 1
  maxConcurrentTasks Int @default(5)
  currentTaskCount   Int @default(0)
  status            UserStatus @default(AVAILABLE)

  // NextAuth fields
  emailVerified DateTime?
  image         String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  assignedTasks Task[]
  taskEvents    TaskEvent[]
  accounts      Account[]
  sessions      Session[]
  skills       AgentSkill[]
  metrics     AgentMetrics[]
  taskMetrics TaskMetrics[]
  workingHours    WorkingHours[]
  availability    AgentAvailability[]
  performanceMetrics PerformanceMetric[]
  defaultTimeZone String?          @map("default_timezone")

  @@index([organizationId])
  @@index([organizationId, status])
  @@index([email])
  @@map("users")
}

model Task {
  id             String   @id @default(cuid())
  organizationId String
  title          String
  description    String?
  priority       TaskPriority @default(MEDIUM)
  type           String   @default("general")
  estimatedDuration Int?  // minutes
  source         String   @default("manual")

  // Assignment tracking
  assignedTo     String?
  assignedAt     DateTime?
  status         TaskStatus @default(PENDING)

  // SLA tracking (Phase 2 enhanced)
  slaPolicy     SLAPolicy? @relation(fields: [slaPolicyId], references: [id])
  slaPolicyId   String?    @map("sla_policy_id")
  responseBy    DateTime?  @map("response_by")
  resolveBy     DateTime?  @map("resolve_by")
  respondedAt   DateTime?  @map("responded_at")
  resolvedAt    DateTime?  @map("resolved_at")
  breachedSLA   Boolean    @default(false) @map("breached_sla")
  escalations   Escalation[]

  // Task metadata for routing and assignment details
  metadata       Json?    @default("{}")
  appliedRules   Json?    @map("applied_rules") // Array of rule IDs and results
  ruleExecutions RuleExecution[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  assignedUser User?        @relation(fields: [assignedTo], references: [id], onDelete: SetNull)
  events       TaskEvent[]
  requiredSkills TaskRequiredSkill[]
  metrics     TaskMetrics?
  rating      TaskRating?

  @@index([organizationId])
  @@index([organizationId, status])
  @@index([assignedTo])
  @@index([status])
  @@index([slaPolicyId])
  @@map("tasks")
}

model TaskEvent {
  id           String    @id @default(cuid())
  taskId       String
  userId       String?
  type         TaskEventType
  data         Json?
  escalationId String?   @map("escalation_id")
  createdAt    DateTime @default(now())

  // Relations
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([taskId])
  @@index([createdAt])
  @@map("task_events")
}

// ============================================================================
// PHASE 2: Skills Management Models
// ============================================================================

model AgentSkill {
  id               String   @id @default(cuid())
  agentId          String   @map("agent_id")
  skillName        String   @map("skill_name")
  proficiencyLevel Int      @default(1) @map("proficiency_level")
  createdAt        DateTime @default(now()) @map("created_at")
   
  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)
   
  @@unique([agentId, skillName])
  @@map("agent_skills")
}

model TaskRequiredSkill {
  id            String   @id @default(cuid())
  taskId        String   @map("task_id")
  skillName     String   @map("skill_name")
  requiredLevel Int      @default(1) @map("required_level")
  createdAt     DateTime @default(now()) @map("created_at")
   
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
   
  @@unique([taskId, skillName])
  @@map("task_required_skills")
}

model SkillsCatalog {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  description    String?
  category       String?
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
   
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
   
  @@unique([organizationId, name])
  @@map("skills_catalog")
}

// ============================================================================
// PHASE 2: Enhanced Routing Models
// ============================================================================

model AgentMetrics {
  id              String   @id @default(cuid())
  agentId         String   @map("agent_id")
  period          String   // 'daily', 'weekly', 'monthly'
  startDate       DateTime @map("start_date")
  endDate         DateTime @map("end_date")
  
  // Task metrics
  tasksAssigned   Int      @default(0) @map("tasks_assigned")
  tasksCompleted  Int      @default(0) @map("tasks_completed")
  tasksEscalated  Int      @default(0) @map("tasks_escalated")
  
  // Time metrics (in minutes)
  totalWorkTime   Int      @default(0) @map("total_work_time")
  activeTime      Int      @default(0) @map("active_time")
  avgResponseTime Float    @default(0) @map("avg_response_time")
  avgHandleTime   Float    @default(0) @map("avg_handle_time")
  
  // Performance scores
  completionRate  Float    @default(0) @map("completion_rate")
  qualityScore    Float    @default(0) @map("quality_score")
  slaComplianceRate Float  @default(0) @map("sla_compliance_rate")
  
  // Relations
  agent           User     @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([agentId, period, startDate])
  @@index([agentId, period])
  @@map("agent_metrics")
}

model TaskMetrics {
  id              String   @id @default(cuid())
  taskId          String   @unique @map("task_id")
  assignedAgentId String?  @map("assigned_agent_id")
  
  // Time tracking
  createdAt       DateTime @default(now()) @map("created_at")
  assignedAt      DateTime? @map("assigned_at")
  firstResponseAt DateTime? @map("first_response_at")
  resolvedAt      DateTime? @map("resolved_at")
  
  // Duration calculations (in minutes)
  waitTime        Int?     @map("wait_time")
  handleTime      Int?     @map("handle_time")
  totalTime       Int?     @map("total_time")
  
  // SLA metrics
  slaPolicy       String?  @map("sla_policy")
  responseTarget  Int?     @map("response_target")
  resolveTarget   Int?     @map("resolve_target")
  metResponseSLA  Boolean? @map("met_response_sla")
  metResolveSLA   Boolean? @map("met_resolve_sla")
  
  // Quality metrics
  complexity      Int?     // 1-5 scale
  quality         Int?     // 1-5 scale
  customerRating  Int?     @map("customer_rating") // 1-5 scale
  
  // Relations
  task           Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  agent          User?    @relation(fields: [assignedAgentId], references: [id])

  @@index([taskId])
  @@index([assignedAgentId])
  @@map("task_metrics")
}

model TeamMetrics {
  id              String   @id @default(cuid())
  organizationId  String   @map("organization_id")
  period          String   // 'daily', 'weekly', 'monthly'
  startDate       DateTime @map("start_date")
  endDate         DateTime @map("end_date")
  
  // Workload metrics
  totalTasks      Int      @default(0) @map("total_tasks")
  completedTasks  Int      @default(0) @map("completed_tasks")
  escalatedTasks  Int      @default(0) @map("escalated_tasks")
  
  // Performance metrics
  avgResponseTime Float    @default(0) @map("avg_response_time")
  avgHandleTime   Float    @default(0) @map("avg_handle_time")
  slaComplianceRate Float  @default(0) @map("sla_compliance_rate")
  customerSatisfaction Float @default(0) @map("customer_satisfaction")
  
  // Resource metrics
  activeAgents    Int      @default(0) @map("active_agents")
  utilizationRate Float    @default(0) @map("utilization_rate")
  
  // Relations
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, period, startDate])
  @@index([organizationId, period])
  @@map("team_metrics")
}

model RoutingStrategy {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  strategy       String   // 'round_robin', 'weighted_round_robin', 'best_match', 'performance_based', 'hybrid'
  isDefault      Boolean  @default(false) @map("is_default")
  isActive       Boolean  @default(true) @map("is_active")
  configuration  Json     @default("{}")  // Strategy-specific config
  createdAt      DateTime @default(now()) @map("created_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([organizationId, name])
  @@map("routing_strategies")
}

// ============================================================================
// PHASE 2: Routing Rules Models
// ============================================================================

model RoutingRule {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  description    String?
  priority       Int      @default(0)
  isActive       Boolean  @default(true) @map("is_active")
  conditions     Json     // Array of conditions
  actions        Json     // Array of actions
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@index([organizationId, priority])
  @@map("routing_rules")
}

model RuleExecution {
  id         String   @id @default(cuid())
  ruleId     String   @map("rule_id")
  taskId     String   @map("task_id")
  succeeded  Boolean  @default(true)
  details    Json?    // Execution details and results
  createdAt  DateTime @default(now()) @map("created_at")

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@index([taskId])
  @@map("rule_executions")
}

// ============================================================================
// PHASE 2: SLA and Escalation Management Models
// ============================================================================

model SLAPolicy {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  description    String?
  priority       String   // 'low', 'medium', 'high', 'urgent'
  responseTime   Int      @map("response_time") // minutes
  resolutionTime Int      @map("resolution_time") // minutes
  escalationRules Json    // Array of escalation rules
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  tasks        Task[]

  @@unique([organizationId, name])
  @@map("sla_policies")
}

model Escalation {
  id             String   @id @default(cuid())
  taskId         String   @map("task_id")
  level          Int      // Escalation level
  reason         String   // 'response_time', 'resolution_time', 'manual'
  status         String   // 'pending', 'active', 'resolved'
  escalatedTo    String?  @map("escalated_to") // User ID
  escalatedAt    DateTime @default(now()) @map("escalated_at")
  resolvedAt     DateTime? @map("resolved_at")
  comment        String?
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@index([taskId, status])
  @@map("escalations")
}

// ============================================================================
// PHASE 2: Performance Metrics Models
// ============================================================================

model PerformanceMetric {
  id             String   @id @default(cuid())
  agentId        String   @map("agent_id")
  organizationId String   @map("organization_id")
  metricType     String   @map("metric_type") // 'daily', 'weekly', 'monthly'
  periodStart    DateTime @map("period_start")
  periodEnd      DateTime @map("period_end")
  
  // Task metrics
  tasksAssigned     Int @default(0) @map("tasks_assigned")
  tasksCompleted    Int @default(0) @map("tasks_completed")
  tasksEscalated    Int @default(0) @map("tasks_escalated")
  
  // Time metrics (in minutes)
  avgResponseTime   Float @default(0) @map("avg_response_time")
  avgResolutionTime Float @default(0) @map("avg_resolution_time")
  totalWorkTime     Int   @default(0) @map("total_work_time")
  
  // Quality metrics
  qualityScore      Float @default(0) @map("quality_score")
  customerRating    Float @default(0) @map("customer_rating")
  
  // SLA metrics
  slaResponseMet    Int @default(0) @map("sla_response_met")
  slaResolutionMet  Int @default(0) @map("sla_resolution_met")
  slaResponseTotal  Int @default(0) @map("sla_response_total")
  slaResolutionTotal Int @default(0) @map("sla_resolution_total")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  agent        User         @relation(fields: [agentId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([agentId, metricType, periodStart])
  @@map("performance_metrics")
}

model SystemMetric {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  metricType     String   @map("metric_type") // 'hourly', 'daily', 'weekly'
  timestamp      DateTime
  
  // System performance
  totalTasks        Int @default(0) @map("total_tasks")
  tasksRouted       Int @default(0) @map("tasks_routed")
  routingFailures   Int @default(0) @map("routing_failures")
  avgRoutingTime    Float @default(0) @map("avg_routing_time")
  
  // Load balancing
  agentsActive      Int @default(0) @map("agents_active")
  agentsIdle        Int @default(0) @map("agents_idle")
  avgUtilization    Float @default(0) @map("avg_utilization")
  
  // SLA system-wide
  slaCompliance     Float @default(0) @map("sla_compliance")
  escalationRate    Float @default(0) @map("escalation_rate")
  
  createdAt DateTime @default(now()) @map("created_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([organizationId, metricType, timestamp])
  @@map("system_metrics")
}

model TaskRating {
  id         String   @id @default(cuid())
  taskId     String   @unique @map("task_id")
  rating     Int      // 1-5 scale
  feedback   String?
  ratedBy    String   @map("rated_by") // Customer/requester ID
  ratedAt    DateTime @default(now()) @map("rated_at")
  
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@map("task_ratings")
}

// ============================================================================
// PHASE 2: Working Hours and Availability Models
// ============================================================================

model WorkingHours {
  id             String   @id @default(cuid())
  agentId        String   @map("agent_id")
  dayOfWeek      Int     // 0-6 (Sunday-Saturday)
  startTime      String   // HH:mm format
  endTime        String   // HH:mm format
  timeZone       String   @default("UTC")
  isWorkDay      Boolean  @default(true) @map("is_work_day")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([agentId, dayOfWeek])
  @@map("working_hours")
}

model AgentAvailability {
  id             String   @id @default(cuid())
  agentId        String   @map("agent_id")
  status         UserStatus
  statusMessage  String?  @map("status_message")
  startTime      DateTime @default(now()) @map("start_time")
  endTime        DateTime? @map("end_time")
  isScheduled    Boolean  @default(false) @map("is_scheduled")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@index([agentId, status])
  @@map("agent_availability")
}

<<<<<<< HEAD
model OrganizationSettings {
  id                    String   @id @default(cuid())
  organizationId        String   @unique @map("organization_id")
  defaultTimezone       String   @default("UTC") @map("default_timezone")
  businessHoursStart    String   @default("09:00") @map("business_hours_start")
  businessHoursEnd      String   @default("17:00") @map("business_hours_end")
  businessDays          Json     @default("[1,2,3,4,5]") // Monday-Friday
  afterHoursRouting     Boolean  @default(false) @map("after_hours_routing")
  weekendRouting        Boolean  @default(false) @map("weekend_routing")
  urgentTasksOverride   Boolean  @default(true) @map("urgent_tasks_override")
  overrideAgentHours    Boolean  @default(false) @map("override_agent_hours")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("organization_settings")
}

model AgentPerformance {
  id                  String   @id @default(cuid())
  agentId             String   @unique @map("agent_id")
  avgResponseTime     Int      @default(0) @map("avg_response_time") // minutes
  avgResolutionTime   Int      @default(0) @map("avg_resolution_time") // minutes
  completionRate      Float    @default(0.0) @map("completion_rate") // 0.0-1.0
  qualityScore        Float    @default(0.0) @map("quality_score") // 0.0-100.0
  totalTasksCompleted Int      @default(0) @map("total_tasks_completed")
  totalTasksAssigned  Int      @default(0) @map("total_tasks_assigned")
  lastUpdated         DateTime @default(now()) @map("last_updated")

  agent User @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("agent_performance")
}


=======
// ============================================================================
// ENUMS
// ============================================================================
>>>>>>> 57224269182c9b320c5573a86634906d466bc895

enum UserStatus {
  AVAILABLE
  BUSY
  AWAY
  OFFLINE
}

enum UserRole {
  ADMIN
  AGENT
}

enum TaskStatus {
  PENDING
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  ESCALATED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TaskEventType {
  CREATED
  ASSIGNED
  STATUS_CHANGED
  UPDATED
  COMPLETED
  ESCALATED
  COMMENT_ADDED
}
